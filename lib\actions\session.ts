"use server";

import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import {
  getMemberFromPhone,
  autoCompletePastSessions,
} from "@/lib/actions/database";

// Server action: set cookie from phone form and redirect to /appointments
export async function loginWithPhone(formData: FormData) {
  // Accept either raw or formatted phone
  const raw = String(formData.get("phone") || "");
  const digits = raw.replace(/\D/g, "");
  // Keep last 10 digits as local TR number
  const local10 = digits.slice(-10);
  if (local10.length !== 10) {
    redirect("/appointments?error=invalid_phone");
  }

  // Validate membership
  const member = await getMemberFromPhone(local10);
  if (!member) {
    redirect("/appointments?error=no_member");
  }

  // Optional: complete past sessions on login
  await autoCompletePastSessions();

  const store = await cookies();
  store.set("memberPhone", local10, {
    httpOnly: true,
    path: "/",
    sameSite: "lax",
  });

  redirect("/appointments");
}

// Server action: clear cookie and redirect to /appointments
export async function logoutMember() {
  const store = await cookies();
  store.delete("memberPhone");
  redirect("/appointments");
}

// Client-side logout function that clears both context and triggers server action
export function clearClientStorage() {
  // Clear client-side storage (sessionStorage via Zustand persist)
  if (typeof window !== "undefined") {
    sessionStorage.removeItem("appointments-storage");
  }
}
