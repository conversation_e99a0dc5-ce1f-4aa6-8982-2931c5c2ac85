import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import type { MinimalMember } from '@/lib/actions/database';

interface AppointmentsState {
  // User data
  memberPhone: string | null;
  memberInfo: MinimalMember | null;
  
  // Loading states
  isLoading: boolean;
  
  // Actions
  setMemberData: (phone: string, memberInfo: MinimalMember) => void;
  clearMemberData: () => void;
  setLoading: (loading: boolean) => void;
  
  // Initialization
  initializeFromCookie: (phone: string, memberInfo: MinimalMember) => void;
}

export const useAppointmentsStore = create<AppointmentsState>()(
  persist(
    (set, get) => ({
      // Initial state
      memberPhone: null,
      memberInfo: null,
      isLoading: false,

      // Actions
      setMemberData: (phone: string, memberInfo: MinimalMember) => {
        set({
          memberPhone: phone,
          memberInfo,
          isLoading: false,
        });
      },

      clearMemberData: () => {
        set({
          memberPhone: null,
          memberInfo: null,
          isLoading: false,
        });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      initializeFromCookie: (phone: string, memberInfo: MinimalMember) => {
        // Only initialize if we don't already have data or if the phone changed
        const currentState = get();
        if (!currentState.memberPhone || currentState.memberPhone !== phone) {
          set({
            memberPhone: phone,
            memberInfo,
            isLoading: false,
          });
        }
      },
    }),
    {
      name: 'appointments-storage',
      storage: createJSONStorage(() => sessionStorage),
      // Only persist essential data
      partialize: (state) => ({
        memberPhone: state.memberPhone,
        memberInfo: state.memberInfo,
      }),
    }
  )
);

// Helper hooks for easier access
export const useMemberPhone = () => useAppointmentsStore((state) => state.memberPhone);
export const useMemberInfo = () => useAppointmentsStore((state) => state.memberInfo);
export const useAppointmentsLoading = () => useAppointmentsStore((state) => state.isLoading);

// Helper function to check if user is authenticated
export const useIsAuthenticated = () => {
  const memberPhone = useMemberPhone();
  const memberInfo = useMemberInfo();
  return !!(memberPhone && memberInfo);
};
