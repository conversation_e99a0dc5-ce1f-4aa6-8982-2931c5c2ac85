import { redirect } from "next/navigation";
import { getNewAppointmentData } from "@/lib/actions/appointments";
import NewAppointmentClient from "@/components/appointments/NewAppointmentClient";
import { Button } from "@/components/ui/button";
import { Package, ArrowLeft } from "lucide-react";
import Link from "next/link";

export default async function NewAppointmentPage() {
  // Get initial data server-side
  const initialData = await getNewAppointmentData();

  // If no data, redirect to login
  if (!initialData) {
    redirect("/appointments");
  }

  const { memberPhone, memberPackages, bookedDates } = initialData;

  // If no active packages, show no packages message
  if (memberPackages.length === 0) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto p-4">
          <div className="flex flex-col items-center justify-center min-h-[60vh] space-y-6">
            <div className="relative">
              <div className="w-20 h-20 bg-secondary rounded-full flex items-center justify-center">
                <Package className="w-10 h-10 text-muted-foreground" />
              </div>
              <div className="absolute -top-1 -right-1 w-6 h-6 bg-destructive rounded-full flex items-center justify-center">
                <span className="text-xs text-destructive-foreground">!</span>
              </div>
            </div>
            <div className="text-center space-y-3 max-w-md">
              <h2 className="text-2xl font-bold text-foreground">
                Aktif Paketiniz Bulunmuyor
              </h2>
              <p className="text-muted-foreground leading-relaxed">
                Randevu alabilmek için önce bir paket satın almanız gerekiyor.
                Lütfen önce bir paket seçin.
              </p>
            </div>
            <div className="flex gap-3">
              <Link href={"/appointments"} className="flex items-center gap-2">
                <ArrowLeft className="w-4 h-4" />
                Ana Sayfaya Dön
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render both enhanced and fallback versions
  return (
    <div className="min-h-screen bg-background">
      <NewAppointmentClient
        initialMemberPhone={memberPhone}
        initialMemberPackages={memberPackages}
        initialBookedDates={bookedDates}
      />
    </div>
  );
}
