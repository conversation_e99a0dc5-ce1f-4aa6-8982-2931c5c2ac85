"use server";

import { revalidateTag } from "next/cache";
import { redirect } from "next/navigation";
import { cookies } from "next/headers";
import {
  getMemberFromPhone,
  autoCompletePastSessions,
  getMemberAllPackages,
  getMemberScheduledSessions,
  checkExistingAppointment,
  checkTimeSlotCapacity,
  bookSession,
  bookMultipleAppointments,
} from "@/lib/actions/database";
import { isTimeSlotInPast } from "@/components/appointments/shared/appointment-utils";

// Types for form data
export interface BookingSingleFormData {
  memberPackageId: string;
  sessionDate: string;
  sessionTime: string;
}

export interface BookingMultipleFormData {
  memberPackageId: string;
  appointments: Array<{ date: string; time: string }>;
}

export interface BookingResult {
  success: boolean;
  message: string;
  errors?: string[];
}

// Server action for booking a single appointment
export async function bookSingleAppointment(
  formData: FormData
): Promise<BookingResult> {
  try {
    // Get member phone from cookies
    const cookieStore = await cookies();
    const memberPhone = cookieStore.get("memberPhone")?.value;

    if (!memberPhone) {
      return {
        success: false,
        message: "Üye bilgisi bulunamadı. Lütfen tekrar giriş yapın.",
      };
    }

    // Extract form data
    const memberPackageId = formData.get("memberPackageId") as string;
    const sessionDate = formData.get("sessionDate") as string;
    const sessionTime = formData.get("sessionTime") as string;

    // Validate required fields
    if (!memberPackageId || !sessionDate || !sessionTime) {
      return {
        success: false,
        message: "Lütfen tüm alanları doldurun.",
      };
    }

    // Validate date format (YYYY-MM-DD)
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(sessionDate)) {
      return {
        success: false,
        message: "Geçersiz tarih formatı.",
      };
    }

    // Validate time format (HH:MM)
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(sessionTime)) {
      return {
        success: false,
        message: "Geçersiz saat formatı.",
      };
    }

    // Validate date is not in the past
    const selectedDateTime = new Date(`${sessionDate}T${sessionTime}:00`);
    const now = new Date();
    if (selectedDateTime <= now) {
      return {
        success: false,
        message: "Geçmiş tarih ve saatlere randevu alamazsınız.",
      };
    }

    // Validate date is not too far in the future (max 30 days)
    const maxDate = new Date();
    maxDate.setDate(maxDate.getDate() + 30);
    if (selectedDateTime > maxDate) {
      return {
        success: false,
        message: "En fazla 30 gün sonrasına randevu alabilirsiniz.",
      };
    }

    // Auto-complete past sessions first
    await autoCompletePastSessions();

    // Get member info
    const member = await getMemberFromPhone(memberPhone);
    if (!member) {
      return {
        success: false,
        message: "Üye bulunamadı.",
      };
    }

    // Check if time slot is in the past
    if (isTimeSlotInPast(sessionDate, sessionTime)) {
      return {
        success: false,
        message:
          "Geçmiş saatlere randevu alamazsınız. Lütfen gelecek bir zaman seçin.",
      };
    }

    // Check for existing appointment on the same date
    const hasExistingAppointment = await checkExistingAppointment(
      member.id,
      sessionDate
    );

    if (hasExistingAppointment) {
      return {
        success: false,
        message:
          "Bu tarihte zaten bir randevunuz bulunmaktadır. Aynı güne birden fazla randevu alamazsınız.",
      };
    }

    // Check time slot capacity
    const capacityCheck = await checkTimeSlotCapacity(sessionDate, sessionTime);

    if (!capacityCheck.available) {
      return {
        success: false,
        message: `Bu tarih ve saat için kapasite dolu. Şu anda ${capacityCheck.currentCount}/${capacityCheck.maxCapacity} randevu mevcut.`,
      };
    }

    // Book the session
    const session = await bookSession(
      member.id,
      memberPackageId,
      sessionDate,
      sessionTime
    );

    if (!session) {
      return {
        success: false,
        message: "Randevu oluşturulurken hata oluştu.",
      };
    }

    // Revalidate appointments data
    revalidateTag("appointments");
    revalidateTag(`appointments-${member.id}`);

    return {
      success: true,
      message: "Randevu başarıyla oluşturuldu!",
    };
  } catch (error) {
    console.error("Single booking error:", error);
    return {
      success: false,
      message: "Randevu oluşturulurken hata oluştu.",
    };
  }
}

// Server action for booking multiple appointments
export async function bookMultipleAppointmentsAction(
  formData: FormData
): Promise<BookingResult> {
  try {
    // Get member phone from cookies
    const cookieStore = await cookies();
    const memberPhone = cookieStore.get("memberPhone")?.value;

    if (!memberPhone) {
      return {
        success: false,
        message: "Üye bilgisi bulunamadı. Lütfen tekrar giriş yapın.",
      };
    }

    // Extract form data
    const memberPackageId = formData.get("memberPackageId") as string;
    const appointmentsJson = formData.get("appointments") as string;

    // Validate required fields
    if (!memberPackageId || !appointmentsJson) {
      return {
        success: false,
        message: "Lütfen paket seçin ve en az bir randevu ekleyin.",
      };
    }

    let appointments: Array<{ date: string; time: string }>;
    try {
      appointments = JSON.parse(appointmentsJson);
    } catch {
      return {
        success: false,
        message: "Randevu bilgileri geçersiz.",
      };
    }

    if (appointments.length === 0) {
      return {
        success: false,
        message: "Lütfen en az bir randevu seçin.",
      };
    }

    // Auto-complete past sessions first
    await autoCompletePastSessions();

    // Get member info
    const member = await getMemberFromPhone(memberPhone);
    if (!member) {
      return {
        success: false,
        message: "Üye bulunamadı.",
      };
    }

    // Book multiple appointments
    const result = await bookMultipleAppointments(
      member.id,
      memberPackageId,
      appointments
    );

    // Revalidate appointments data
    revalidateTag("appointments");
    revalidateTag(`appointments-${member.id}`);

    if (result.success.length > 0 && result.failed.length === 0) {
      return {
        success: true,
        message: `${result.success.length} randevu başarıyla oluşturuldu!`,
      };
    } else if (result.success.length > 0 && result.failed.length > 0) {
      const failedMessages = result.failed.map(
        (f) => `${f.date} ${f.time}: ${f.error}`
      );
      return {
        success: true,
        message: `${result.success.length} randevu başarıyla oluşturuldu.`,
        errors: failedMessages,
      };
    } else {
      const failedMessages = result.failed.map(
        (f) => `${f.date} ${f.time}: ${f.error}`
      );
      return {
        success: false,
        message: "Hiçbir randevu oluşturulamadı.",
        errors: failedMessages,
      };
    }
  } catch (error) {
    console.error("Multiple booking error:", error);
    return {
      success: false,
      message: "Randevular oluşturulurken hata oluştu.",
    };
  }
}

// Server action to redirect after successful booking
export async function redirectToAppointments() {
  redirect("/appointments");
}

// Server action to get initial data for the new appointment page
export async function getNewAppointmentData() {
  try {
    const cookieStore = await cookies();
    const memberPhone = cookieStore.get("memberPhone")?.value;

    if (!memberPhone) {
      return null;
    }

    // Auto-complete past sessions first
    await autoCompletePastSessions();

    // Get member info
    const member = await getMemberFromPhone(memberPhone);
    if (!member) {
      return null;
    }

    // Get member's active packages and scheduled sessions
    const [memberPackages, bookedDates] = await Promise.all([
      getMemberAllPackages(member.id),
      getMemberScheduledSessions(member.id),
    ]);

    return {
      memberPhone,
      memberPackages,
      bookedDates,
    };
  } catch (error) {
    console.error("Error getting new appointment data:", error);
    return null;
  }
}
